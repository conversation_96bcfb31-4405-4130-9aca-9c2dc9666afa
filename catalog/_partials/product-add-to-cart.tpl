{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 *}
<div class="product-add-to-cart">
{if !$configuration.is_catalog}
<form action="{$urls.pages.cart}?action=add" method="post" id="add-to-cart-or-refresh">
<input type="hidden" name="token" value="{$static_token}">
<input type="hidden" name="id_product" value="{$product.id}">
<input type="hidden" name="id_customization" value="{$product.id_customization}" >

{block name='product_quantity'}
<div class="product-quantity clearfix">
<div class="qty">
<input
type="number"
name="qty"
id="quantity_wanted"
value="{$product.minimal_quantity}"
min="{$product.minimal_quantity}"
step="{$product.minimal_quantity}"
class="input-group"
>
</div>
</div>
{/block}

{block name='product_buttons'}
<div class="product-buttons">
<button class="btn btn-primary add-to-cart" data-button-action="add-to-cart" type="submit">
<span>{l s='Add to cart' d='Shop.Theme.Actions'}</span>
</button>
</div>
{/block}
</form>
{/if}
</div>

{* Custom JS to enforce multiples of minimal quantity *}
{literal}
<script>
document.addEventListener('DOMContentLoaded', () => {
const qtyInput = document.querySelector('#quantity_wanted');
if (qtyInput) {
const getStep = () => parseInt(qtyInput.getAttribute('step')) || 1;
const getMin = () => parseInt(qtyInput.getAttribute('min')) || getStep();
let lastVal = parseInt(qtyInput.value) || getMin();
let typing = false;
let typingTimer;

const snapUp = (val, step) => (val % step === 0 ? val + step : Math.ceil(val / step) * step);
const snapDown = (val, step, min) => {
  let next = (val % step === 0 ? val - step : Math.floor(val / step) * step);
  if (next < min) next = min;
  return next;
};

qtyInput.addEventListener('keydown', (e) => {
  if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
    e.preventDefault();
    const step = getStep(), min = getMin();
    let current = parseInt(qtyInput.value);
    if (isNaN(current) || current < min) current = min;
    const next = e.key === 'ArrowUp' ? snapUp(current, step) : snapDown(current, step, min);
    if (next !== current) {
      qtyInput.value = next;
      qtyInput.dispatchEvent(new Event('input', { bubbles: true }));
      qtyInput.dispatchEvent(new Event('change', { bubbles: true }));
      lastVal = next;
    }
    return;
  }
  // Mark manual typing so we don't snap on input
  if ((e.key.length === 1 && e.key >= '0' && e.key <= '9') || e.key === 'Backspace' || e.key === 'Delete') {
    typing = true;
    clearTimeout(typingTimer);
    typingTimer = setTimeout(() => { typing = false; }, 300);
  }
});

qtyInput.addEventListener('input', () => {
  const step = getStep(), min = getMin();
  let current = parseInt(qtyInput.value);
  if (isNaN(current)) return; // allow free typing until change
  if (typing) { lastVal = current; return; }
  const directionUp = current > lastVal;
  let next = directionUp ? snapUp(current, step) : snapDown(current, step, min);
  if (next !== current) {
    qtyInput.value = next;
    lastVal = next;
  } else {
    lastVal = current;
  }
});

qtyInput.addEventListener('wheel', (e) => {
  if (document.activeElement !== qtyInput) return;
  e.preventDefault();
  const step = getStep(), min = getMin();
  let current = parseInt(qtyInput.value);
  if (isNaN(current) || current < min) current = min;
  const directionUp = e.deltaY < 0;
  const next = directionUp ? snapUp(current, step) : snapDown(current, step, min);
  qtyInput.value = next;
  qtyInput.dispatchEvent(new Event('input', { bubbles: true }));
  qtyInput.dispatchEvent(new Event('change', { bubbles: true }));
  lastVal = next;
}, { passive: false });

qtyInput.addEventListener('change', () => {
  const step = getStep();
  const min = getMin();
  let val = parseInt(qtyInput.value);
  if (isNaN(val) || val < min) {
    qtyInput.value = min;
  } else if (val % step !== 0) {
    qtyInput.value = Math.ceil(val / step) * step;
  }
  lastVal = parseInt(qtyInput.value) || min;
});
    // Make +/- buttons step by minimal quantity (TouchSpin buttons)
    document.addEventListener('click', (e) => {
      const upBtn = e.target.closest('.bootstrap-touchspin-up, .js-touchspin-up');
      const downBtn = e.target.closest('.bootstrap-touchspin-down, .js-touchspin-down');
      if (!upBtn && !downBtn) return;

      // Ensure the click relates to this product's quantity input
      const wrapper = (upBtn || downBtn).closest('.bootstrap-touchspin') || (upBtn || downBtn).closest('.product-quantity');
      const relatedInput = wrapper ? wrapper.querySelector('#quantity_wanted') : null;
      if (!relatedInput || relatedInput !== qtyInput) return;

      e.preventDefault();
      e.stopPropagation();

      const stepNow = parseInt(qtyInput.getAttribute('step')) || 1;
      const minNow = parseInt(qtyInput.getAttribute('min')) || stepNow;

      let current = parseInt(qtyInput.value);
      if (isNaN(current) || current < minNow) current = minNow;

      let next;
      if (upBtn) {
        // If already on a multiple, add one full step; otherwise round up to next multiple
        next = current % stepNow === 0 ? current + stepNow : Math.ceil(current / stepNow) * stepNow;
      } else {
        // If already on a multiple, subtract one full step; otherwise round down to previous multiple
        next = current % stepNow === 0 ? current - stepNow : Math.floor(current / stepNow) * stepNow;
        if (next < minNow) next = minNow;
      }

      if (next !== current) {
        qtyInput.value = next;
        qtyInput.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });

}
});
</script>
{/literal}