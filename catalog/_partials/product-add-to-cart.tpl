{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 *}
<div class="product-add-to-cart">
{if !$configuration.is_catalog}
<form action="{$urls.pages.cart}?action=add" method="post" id="add-to-cart-or-refresh">
<input type="hidden" name="token" value="{$static_token}">
<input type="hidden" name="id_product" value="{$product.id}">
<input type="hidden" name="id_customization" value="{$product.id_customization}" >

{block name='product_quantity'}
<div class="product-quantity clearfix">
<div class="qty">
<input
type="number"
name="qty"
id="quantity_wanted"
value="{$product.minimal_quantity}"
min="{$product.minimal_quantity}"
step="{$product.minimal_quantity}"
class="input-group"
>
</div>
</div>
{/block}

{block name='product_buttons'}
<div class="product-buttons">
<button class="btn btn-primary add-to-cart" data-button-action="add-to-cart" type="submit">
<span>{l s='Add to cart' d='Shop.Theme.Actions'}</span>
</button>
</div>
{/block}
</form>
{/if}
</div>

{* Custom JS to enforce multiples of minimal quantity *}
{literal}
<script>
document.addEventListener('DOMContentLoaded', () => {
  const qtyInput = document.querySelector('#quantity_wanted');
  if (!qtyInput) return;

  const getStep = () => parseInt(qtyInput.getAttribute('step')) || 1;
  const getMin = () => parseInt(qtyInput.getAttribute('min')) || getStep();
  let isProcessing = false;

  const snapToMultiple = (value, step, min, preferUp = true) => {
    if (isNaN(value) || value < min) return min;
    if (value % step === 0) return value;
    return preferUp ? Math.ceil(value / step) * step : Math.floor(value / step) * step;
  };

  const enforceStep = (preferUp = true) => {
    if (isProcessing) return;
    isProcessing = true;

    const step = getStep();
    const min = getMin();
    const current = parseInt(qtyInput.value) || min;
    const corrected = snapToMultiple(current, step, min, preferUp);

    if (corrected !== current) {
      qtyInput.value = corrected;
      // Don't trigger PrestaShop AJAX - just update locally
      console.log('Quantity corrected:', current, '→', corrected);
    }

    setTimeout(() => { isProcessing = false; }, 50);
  };

  // Handle manual typing - snap on blur/change
  qtyInput.addEventListener('change', () => {
    enforceStep(true); // Always snap up on manual input
  });

  // Handle keyboard arrows
  qtyInput.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      e.preventDefault();
      const step = getStep();
      const min = getMin();
      const current = parseInt(qtyInput.value) || min;

      let next;
      if (e.key === 'ArrowUp') {
        next = current % step === 0 ? current + step : Math.ceil(current / step) * step;
      } else {
        next = current % step === 0 ? current - step : Math.floor(current / step) * step;
        if (next < min) next = min;
      }

      qtyInput.value = next;
      qtyInput.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });

  // Listen for PrestaShop quantity updates (from spinner buttons or AJAX)
  if (typeof prestashop !== 'undefined') {
    prestashop.on('updatedProductQuantity', () => {
      setTimeout(() => enforceStep(true), 50);
    });

    prestashop.on('updatedProduct', () => {
      setTimeout(() => enforceStep(true), 50);
    });
  }

  // Monitor direct value property changes (but not too aggressively)
  let lastValue = qtyInput.value;
  setInterval(() => {
    if (qtyInput.value !== lastValue && !isProcessing) {
      lastValue = qtyInput.value;
      enforceStep(true);
    }
  }, 200);

});
</script>
{/literal}