{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 *}
<div class="product-add-to-cart">
{if !$configuration.is_catalog}
<form action="{$urls.pages.cart}?action=add" method="post" id="add-to-cart-or-refresh">
<input type="hidden" name="token" value="{$static_token}">
<input type="hidden" name="id_product" value="{$product.id}">
<input type="hidden" name="id_customization" value="{$product.id_customization}" >

{block name='product_quantity'}
<div class="product-quantity clearfix">
<div class="qty">
<input
type="number"
name="qty"
id="quantity_wanted"
value="{$product.minimal_quantity}"
min="{$product.minimal_quantity}"
step="{$product.minimal_quantity}"
class="input-group"
>
</div>
</div>
{/block}

{block name='product_buttons'}
<div class="product-buttons">
<button class="btn btn-primary add-to-cart" data-button-action="add-to-cart" type="submit">
<span>{l s='Add to cart' d='Shop.Theme.Actions'}</span>
</button>
</div>
{/block}
</form>
{/if}
</div>

{* Custom JS to enforce multiples of minimal quantity *}
{literal}
<script>
// Execute immediately or on DOMContentLoaded
(function() {
  const initQuantityFix = () => {
    const qtyInput = document.querySelector('#quantity_wanted');
    if (!qtyInput) {
      console.log('Quantity input not found, retrying...');
      return false;
    }

    console.log('Quantity fix initialized!');

    const getStep = () => parseInt(qtyInput.getAttribute('step')) || 1;
    const getMin = () => parseInt(qtyInput.getAttribute('min')) || getStep();
    let isUserTyping = false;
    let lastKnownValue = parseInt(qtyInput.value) || getMin();

    // Handle keyboard arrows - direct control
    qtyInput.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();
        const step = getStep();
        const min = getMin();
        const current = parseInt(qtyInput.value) || min;

        let next;
        if (e.key === 'ArrowUp') {
          next = current % step === 0 ? current + step : Math.ceil(current / step) * step;
        } else {
          next = current % step === 0 ? current - step : Math.floor(current / step) * step;
          if (next < min) next = min;
        }

        qtyInput.value = next;
        lastKnownValue = next;
        console.log('Arrow key:', e.key, current, '→', next);

        // Prevent PrestaShop AJAX by stopping all events
        e.stopPropagation();
        e.stopImmediatePropagation();
        return;
      }

      // Mark user typing
      if ((e.key.length === 1 && e.key >= '0' && e.key <= '9') || e.key === 'Backspace' || e.key === 'Delete') {
        isUserTyping = true;
      }
    });

    // Handle manual typing - snap on blur/change
    qtyInput.addEventListener('change', () => {
      if (!isUserTyping) return; // Only process manual changes

      const step = getStep();
      const min = getMin();
      let val = parseInt(qtyInput.value);

      if (isNaN(val) || val < min) {
        qtyInput.value = min;
      } else if (val % step !== 0) {
        qtyInput.value = Math.ceil(val / step) * step;
      }

      lastKnownValue = parseInt(qtyInput.value);
      isUserTyping = false;
      console.log('Manual input corrected to:', qtyInput.value);
    });

    // Monitor for spinner button changes via value monitoring
    let monitoringActive = true;
    setInterval(() => {
      if (!monitoringActive || isUserTyping) return;

      const current = parseInt(qtyInput.value) || getMin();
      if (current !== lastKnownValue) {
        const step = getStep();
        const min = getMin();
        const direction = current > lastKnownValue ? 'up' : 'down';
        let corrected;

        if (direction === 'up') {
          corrected = current % step === 0 ? current : Math.ceil(current / step) * step;
        } else {
          corrected = current % step === 0 ? current : Math.floor(current / step) * step;
          if (corrected < min) corrected = min;
        }

        if (corrected !== current) {
          monitoringActive = false; // Prevent recursion
          qtyInput.value = corrected;
          console.log('Spinner corrected:', direction, current, '→', corrected);
          setTimeout(() => { monitoringActive = true; }, 100);
        }

        lastKnownValue = parseInt(qtyInput.value);
      }
    }, 50);

    // Also try PrestaShop events as backup
    if (typeof prestashop !== 'undefined') {
      prestashop.on('updatedProductQuantity', () => {
        console.log('🔥 PrestaShop updatedProductQuantity detected');
        setTimeout(() => {
          const current = parseInt(qtyInput.value) || getMin();
          if (!isUserTyping && current !== lastKnownValue) {
            console.log('Processing PrestaShop quantity change:', lastKnownValue, '→', current);
          }
        }, 10);
      });
    }

    return true;
  };

  // Try to initialize immediately
  if (initQuantityFix() === false) {
    // If failed, wait for DOM or try again
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initQuantityFix);
    } else {
      // DOM already loaded, try again after short delay
      setTimeout(initQuantityFix, 100);
    }
  }
})();
</script>
{/literal}